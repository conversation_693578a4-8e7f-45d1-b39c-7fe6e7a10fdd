# -*- coding: utf-8 -*-
"""
PPT图片获取服务
负责生成和管理PPT中使用的各种图片
"""

import logging
import os
from typing import Dict, Any, Optional
import asyncio

from core.models import QueryParams
from .picture.PictureSave import create_image_manager
from .picture.NewMembersPic import create_new_members_pic_generator

logger = logging.getLogger(__name__)

class PicAcquisitionService:
    """图片获取服务类"""

    def __init__(self):
        """初始化图片获取服务"""
        logger.info("图片获取服务初始化完成")

    def _extract_bid(self, query_params):
        """
        从查询参数中提取bid

        Args:
            query_params: 查询参数（可能是对象或字典）

        Returns:
            str: bid值
        """
        if hasattr(query_params, 'bid'):
            return query_params.bid
        elif isinstance(query_params, dict):
            return query_params.get('bid')
        else:
            raise ValueError("无法从查询参数中提取bid")

    async def generate_all_pictures(self, query_params) -> Dict[str, str]:
        """
        生成PPT所需的所有图片

        Args:
            query_params: 查询参数（对象或字典）

        Returns:
            Dict: 图片类型到路径的映射
        """
        try:
            bid = self._extract_bid(query_params)
            logger.info(f"开始生成PPT图片 - bid: {bid}")

            # 创建图片管理器
            image_manager = create_image_manager(bid)

            # 生成各类图片
            result = {}

            # 生成新增会员图片
            new_members_pics = await self._generate_new_members_pictures(query_params, image_manager)
            result.update(new_members_pics)

            # 生成会员消费图片
            consumption_pics = await self._generate_member_consumption_pictures(query_params, image_manager)
            result.update(consumption_pics)

            # 生成会员平均消费图片
            avg_consumption_pics = await self._generate_avg_consumption_pictures(query_params, image_manager)
            result.update(avg_consumption_pics)

            # 生成会员消费数量图片
            consumption_num_pics = await self._generate_consumption_num_pictures(query_params, image_manager)
            result.update(consumption_num_pics)

            logger.info(f"PPT图片生成完成 - 共生成 {len(result)} 张图片")
            return result

        except Exception as e:
            logger.error(f"生成PPT图片失败: {e}")
            return {}

    async def _generate_new_members_pictures(self, query_params, image_manager) -> Dict[str, str]:
        """
        生成新增会员相关图片

        Args:
            query_params: 查询参数（对象或字典）
            image_manager: 图片管理器

        Returns:
            Dict: 新增会员图片路径映射
        """
        try:
            logger.info("开始生成新增会员图片")

            bid = self._extract_bid(query_params)
            # 创建新增会员图片生成器
            generator = create_new_members_pic_generator(bid, image_manager)

            # 生成图片
            result = await generator.generate_new_members_charts(query_params)

            logger.info(f"新增会员图片生成完成: {list(result.keys())}")
            return result

        except Exception as e:
            logger.error(f"生成新增会员图片失败: {e}")
            return {}

    async def _generate_member_consumption_pictures(self, query_params, image_manager) -> Dict[str, str]:
        """
        生成会员消费相关图片

        Args:
            query_params: 查询参数（对象或字典）
            image_manager: 图片管理器

        Returns:
            Dict: 图片路径字典
        """
        try:
            from .picture.MemberConsumptionPic import create_member_consumption_pic_generator

            bid = self._extract_bid(query_params)
            # 创建会员消费图片生成器
            generator = create_member_consumption_pic_generator(bid, image_manager)

            # 生成图片
            result = await generator.generate_member_consumption_charts(query_params)

            logger.info(f"会员消费图片生成完成: {list(result.keys())}")
            return result

        except Exception as e:
            logger.error(f"生成会员消费图片失败: {e}")
            return {}

    async def _generate_avg_consumption_pictures(self, query_params, image_manager) -> Dict[str, str]:
        """
        生成会员平均消费相关图片

        Args:
            query_params: 查询参数（对象或字典）
            image_manager: 图片管理器

        Returns:
            Dict: 图片路径字典
        """
        try:
            from .picture.AvgConsumptionPic import create_avg_consumption_pic_generator

            bid = self._extract_bid(query_params)
            # 创建会员平均消费图片生成器
            generator = create_avg_consumption_pic_generator(bid, image_manager)

            # 生成图片
            result = await generator.generate_avg_consumption_charts(query_params)

            logger.info(f"会员平均消费图片生成完成: {list(result.keys())}")
            return result

        except Exception as e:
            logger.error(f"生成会员平均消费图片失败: {e}")
            return {}

    async def _generate_consumption_num_pictures(self, query_params, image_manager) -> Dict[str, str]:
        """
        生成会员消费数量相关图片

        Args:
            query_params: 查询参数（对象或字典）
            image_manager: 图片管理器

        Returns:
            Dict: 图片路径字典
        """
        try:
            from .picture.ConsumptionNumPic import create_consumption_num_pic_generator

            bid = self._extract_bid(query_params)
            # 创建会员消费数量图片生成器
            generator = create_consumption_num_pic_generator(bid, image_manager)

            # 生成图片
            result = await generator.generate_consumption_num_charts(query_params)

            logger.info(f"会员消费数量图片生成完成: {list(result.keys())}")
            return result

        except Exception as e:
            logger.error(f"生成会员消费数量图片失败: {e}")
            return {}

    async def get_picture_paths(self, query_params) -> Dict[str, str]:
        """
        获取图片路径（如果不存在则生成）

        Args:
            query_params: 查询参数（对象或字典）

        Returns:
            Dict: 图片类型到路径的映射
        """
        try:
            bid = self._extract_bid(query_params)
            # 获取基础图片路径配置
            from .PictureConstants import get_image_params
            existing_paths = get_image_params(bid)

            # 始终生成所有图片和AI分析（确保数据最新）
            logger.info("开始生成所有图片和AI分析（覆盖模式）")
            generated_pics = await self.generate_all_pictures(query_params)

            # 更新所有新增会员相关的图片路径
            new_member_pic_types = ['new_member_add_last_year', 'new_member_add_this_year']
            for pic_type in new_member_pic_types:
                if pic_type in generated_pics:
                    existing_paths[f'image_{pic_type}'] = generated_pics[pic_type]
                    logger.info(f"更新图片路径: image_{pic_type} -> {generated_pics[pic_type]}")

            # 更新所有会员消费相关的图片路径
            member_consumption_pic_types = ['member_consumption_last_year', 'member_consumption_this_year']
            for pic_type in member_consumption_pic_types:
                if pic_type in generated_pics:
                    existing_paths[f'image_{pic_type}'] = generated_pics[pic_type]
                    logger.info(f"更新图片路径: image_{pic_type} -> {generated_pics[pic_type]}")

            # 更新所有会员平均消费相关的图片路径
            avg_consumption_pic_types = ['avg_consumption_last_year', 'avg_consumption_this_year']
            for pic_type in avg_consumption_pic_types:
                if pic_type in generated_pics:
                    existing_paths[f'image_{pic_type}'] = generated_pics[pic_type]
                    logger.info(f"更新图片路径: image_{pic_type} -> {generated_pics[pic_type]}")

            # 更新所有会员消费数量相关的图片路径
            consumption_num_pic_types = ['consumption_num_last_year', 'consumption_num_this_year']
            for pic_type in consumption_num_pic_types:
                if pic_type in generated_pics:
                    existing_paths[f'image_{pic_type}'] = generated_pics[pic_type]
                    logger.info(f"更新图片路径: image_{pic_type} -> {generated_pics[pic_type]}")

            # 添加AI分析参数（确保AI分析始终是最新的）
            ai_analysis_params = [
                "new_member_add_last_year_analysis_report",
                "new_member_add_this_year_analysis_report",
                "member_consumption_last_year_analysis_report",
                "member_consumption_this_year_analysis_report",
                "avg_consumption_last_year_analysis_report",
                "avg_consumption_this_year_analysis_report",
                "consumption_num_last_year_analysis_report",
                "consumption_num_this_year_analysis_report"
            ]

            for ai_param in ai_analysis_params:
                if ai_param in generated_pics:
                    existing_paths[ai_param] = generated_pics[ai_param]
                    logger.info(f"添加AI分析参数: {ai_param}")

            logger.info(f"图片和AI分析生成完成，共返回 {len(existing_paths)} 个参数")
            return existing_paths

        except Exception as e:
            logger.error(f"获取图片路径失败: {e}")
            return {}


# 创建服务实例
pic_acquisition_service = PicAcquisitionService()