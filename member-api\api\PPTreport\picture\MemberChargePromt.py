# -*- coding: utf-8 -*-
"""
会员充值消费数据AI分析提示词模板
提供会员充值消费数据的智能分析提示词生成功能
"""

from typing import Dict, Any, List
import random

class MemberChargeAnalysisPrompts:
    """会员充值消费数据AI分析提示词类"""

    @staticmethod
    def get_member_charge_last_year_analysis_prompt(monthly_data: List[Dict[str, Any]]) -> str:
        """
        生成去年会员充值消费数据分析提示词

        Args:
            monthly_data: 去年月度充值消费数据列表

        Returns:
            str: 分析提示词
        """
        if not monthly_data:
            return "去年会员充值消费数据不足，无法进行有效分析。"

        try:
            # 提取关键数据
            charge_amounts = [item.get('charge_amount', 0) for item in monthly_data]
            consume_amounts = [item.get('period_charge_amount_unused', 0) for item in monthly_data]
            present_amounts = [item.get('period_charge_present', 0) for item in monthly_data]
            retention_rates = [item.get('retention_rate', 0) for item in monthly_data]

            # 计算基础统计
            avg_charge = sum(charge_amounts) / len(charge_amounts)
            avg_consume = sum(consume_amounts) / len(consume_amounts)
            avg_present = sum(present_amounts) / len(present_amounts)
            avg_retention = sum(retention_rates) / len(retention_rates)

            total_charge = sum(charge_amounts)
            total_consume = sum(consume_amounts)
            total_present = sum(present_amounts)

            # 找出关键月份
            max_charge_idx = charge_amounts.index(max(charge_amounts))
            min_charge_idx = charge_amounts.index(min(charge_amounts))
            max_retention_idx = retention_rates.index(max(retention_rates))
            min_retention_idx = retention_rates.index(min(retention_rates))

            peak_charge_month = monthly_data[max_charge_idx]['month']
            valley_charge_month = monthly_data[min_charge_idx]['month']
            best_retention_month = monthly_data[max_retention_idx]['month']
            worst_retention_month = monthly_data[min_retention_idx]['month']

            peak_charge_value = max(charge_amounts)
            valley_charge_value = min(charge_amounts)
            best_retention_value = max(retention_rates)
            worst_retention_value = min(retention_rates)

            # 计算充值赠送比例
            present_ratio = (total_present / total_charge * 100) if total_charge > 0 else 0
            consume_ratio = (total_consume / total_charge * 100) if total_charge > 0 else 0

            # 计算波动性
            charge_variance = sum((x - avg_charge) ** 2 for x in charge_amounts) / len(charge_amounts)
            charge_volatility = (charge_variance ** 0.5) / avg_charge * 100 if avg_charge > 0 else 0

            # 生成按点分析
            analysis_points = []

            # 1. 充值规模洞察 + 实操建议
            if peak_charge_value > avg_charge * 1.5:
                analysis_points.append(f"1. 充值高峰集中在{peak_charge_month}月({peak_charge_value:.1f}万元)，建议复制该月成功策略并在其他月份推广应用")
            elif charge_volatility > 30:
                analysis_points.append(f"1. 充值波动较大(波动率{charge_volatility:.1f}%)，建议建立稳定的充值激励机制减少月度差异")
            else:
                analysis_points.append(f"1. 充值表现稳定月均{avg_charge:.1f}万元，建议通过会员分层策略进一步提升充值规模")

            # 2. 储值消耗洞察 + 实操建议
            if consume_ratio > 80:
                analysis_points.append(f"2. 储值消耗率{consume_ratio:.1f}%表现优秀，建议优化储值权益增加用户粘性和复购频次")
            elif consume_ratio < 50:
                analysis_points.append(f"2. 储值消耗率{consume_ratio:.1f}%偏低，建议推出储值专享活动和限时优惠提升使用率")
            else:
                analysis_points.append(f"2. 储值消耗率{consume_ratio:.1f}%中等水平，建议通过个性化推荐提升储值使用效率")

            # 3. 赠送策略洞察 + 实操建议
            if present_ratio > 15:
                analysis_points.append(f"3. 充值赠送比例{present_ratio:.1f}%较高，建议评估赠送ROI并优化赠送策略提升转化效果")
            elif present_ratio < 5:
                analysis_points.append(f"3. 充值赠送比例{present_ratio:.1f}%偏低，建议适度增加赠送力度刺激用户充值意愿")
            else:
                analysis_points.append(f"3. 充值赠送比例{present_ratio:.1f}%合理，建议根据用户价值分层实施差异化赠送策略")

            # 4. 留存效果洞察 + 实操建议
            retention_gap = best_retention_value - worst_retention_value
            if retention_gap > 20:
                analysis_points.append(f"4. 留存率差异明显({best_retention_month}月{best_retention_value:.1f}%vs{worst_retention_month}月{worst_retention_value:.1f}%)，建议分析高留存月份的成功因素")
            elif avg_retention > 70:
                analysis_points.append(f"4. 储值留存率{avg_retention:.1f}%表现良好，建议建立留存预警机制防止用户流失")
            else:
                analysis_points.append(f"4. 储值留存率{avg_retention:.1f}%有提升空间，建议通过会员权益和服务体验提升用户粘性")

            return "\n".join(analysis_points)

        except Exception as e:
            return f"去年会员充值消费数据分析生成失败: {str(e)}"

    @staticmethod
    def get_member_charge_this_year_analysis_prompt(
        this_year_data: List[Dict[str, Any]],
        last_year_data: List[Dict[str, Any]]
    ) -> str:
        """
        生成今年会员充值消费数据对比分析提示词

        Args:
            this_year_data: 今年月度充值消费数据列表
            last_year_data: 去年同期数据列表（用于对比）

        Returns:
            str: 对比分析提示词
        """
        if not this_year_data:
            return "今年会员充值消费数据不足，无法进行有效分析。"

        try:
            # 今年数据统计
            this_charge_amounts = [item.get('charge_amount', 0) for item in this_year_data]
            this_consume_amounts = [item.get('period_charge_amount_unused', 0) for item in this_year_data]
            this_present_amounts = [item.get('period_charge_present', 0) for item in this_year_data]
            this_retention_rates = [item.get('retention_rate', 0) for item in this_year_data]

            this_avg_charge = sum(this_charge_amounts) / len(this_charge_amounts)
            this_avg_retention = sum(this_retention_rates) / len(this_retention_rates)

            this_total_charge = sum(this_charge_amounts)
            this_total_consume = sum(this_consume_amounts)
            this_total_present = sum(this_present_amounts)

            # 计算同比变化
            growth_charge = 0
            growth_consume = 0
            growth_present = 0
            retention_change = 0

            if last_year_data:
                # 取去年同期数据（相同月份数量）
                last_year_same_period = last_year_data[:len(this_year_data)]

                last_charge_amounts = [item.get('charge_amount', 0) for item in last_year_same_period]
                last_consume_amounts = [item.get('period_charge_amount_unused', 0) for item in last_year_same_period]
                last_present_amounts = [item.get('period_charge_present', 0) for item in last_year_same_period]
                last_retention_rates = [item.get('retention_rate', 0) for item in last_year_same_period]

                last_total_charge = sum(last_charge_amounts)
                last_total_consume = sum(last_consume_amounts)
                last_total_present = sum(last_present_amounts)
                last_avg_retention = sum(last_retention_rates) / len(last_retention_rates)

                # 计算增长率
                growth_charge = ((this_total_charge - last_total_charge) / last_total_charge * 100) if last_total_charge > 0 else 0
                growth_consume = ((this_total_consume - last_total_consume) / last_total_consume * 100) if last_total_consume > 0 else 0
                growth_present = ((this_total_present - last_total_present) / last_total_present * 100) if last_total_present > 0 else 0
                retention_change = this_avg_retention - last_avg_retention

            # 计算今年的比例
            this_present_ratio = (this_total_present / this_total_charge * 100) if this_total_charge > 0 else 0
            this_consume_ratio = (this_total_consume / this_total_charge * 100) if this_total_charge > 0 else 0

            # 生成按点对比分析
            analysis_points = []

            # 1. 充值规模对比洞察 + 实操建议
            if growth_charge > 20:
                analysis_points.append(f"1. 充值规模同比增长{growth_charge:.1f}%表现优异，建议加大成功策略投入并扩展到更多用户群体")
            elif growth_charge < -10:
                analysis_points.append(f"1. 充值规模同比下降{abs(growth_charge):.1f}%需要关注，建议分析下降原因并制定针对性挽回策略")
            else:
                analysis_points.append(f"1. 充值规模同比变化{growth_charge:.1f}%相对稳定，建议通过创新充值产品突破增长瓶颈")

            # 2. 储值消耗对比洞察 + 实操建议
            if growth_consume > 15:
                analysis_points.append(f"2. 储值消耗同比增长{growth_consume:.1f}%表现良好，建议继续优化储值使用场景提升用户体验")
            elif growth_consume < -5:
                analysis_points.append(f"2. 储值消耗同比下降{abs(growth_consume):.1f}%，建议推出储值激活活动提升使用积极性")
            else:
                analysis_points.append(f"2. 储值消耗同比变化{growth_consume:.1f}%，建议通过数据分析优化储值产品设计")

            # 3. 赠送策略对比洞察 + 实操建议
            if growth_present > 30:
                analysis_points.append(f"3. 充值赠送同比增长{growth_present:.1f}%投入较大，建议评估赠送效果并优化投入产出比")
            elif growth_present < -20:
                analysis_points.append(f"3. 充值赠送同比减少{abs(growth_present):.1f}%，建议适度恢复赠送力度维持用户充值动力")
            else:
                analysis_points.append(f"3. 充值赠送同比变化{growth_present:.1f}%，建议基于用户行为数据精准投放赠送资源")

            # 4. 留存效果对比洞察 + 实操建议
            if retention_change > 5:
                analysis_points.append(f"4. 储值留存率同比提升{retention_change:.1f}%效果显著，建议总结成功经验并在其他业务线推广")
            elif retention_change < -5:
                analysis_points.append(f"4. 储值留存率同比下降{abs(retention_change):.1f}%需要重视，建议加强用户关怀和服务质量提升")
            else:
                analysis_points.append(f"4. 储值留存率同比变化{retention_change:.1f}%基本稳定，建议通过个性化服务进一步提升用户满意度")

            return "\n".join(analysis_points)

        except Exception as e:
            return f"今年会员充值消费数据对比分析生成失败: {str(e)}"


# 创建全局提示词实例
member_charge_analysis_prompts = MemberChargeAnalysisPrompts()