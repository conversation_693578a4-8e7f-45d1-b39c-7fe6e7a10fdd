# -*- coding: utf-8 -*-
"""
会员平均消费数据AI分析提示词模板
针对会员平均消费数据（首次消费金额、再次消费金额、会员单均消费、会员人均贡献）提供专业的AI分析模板
"""

from typing import Dict, Any, List
import random

class AvgConsumptionAnalysisPrompts:
    """会员平均消费数据AI分析提示词类"""

    # 平均消费数据分析模板句式（去年数据）
    AVG_CONSUMPTION_LAST_YEAR_TEMPLATES = [
        "去年全年平均消费数据显示，首次消费金额月均{avg_first_consume:.1f}元，再次消费金额月均{avg_repeat_consume:.1f}元，{first_repeat_analysis}。",
        "去年会员单均消费表现为月均{avg_consume_amount:.1f}元，会员人均贡献月均{avg_contribution:.1f}元，{consumption_efficiency_analysis}。",
        "去年消费结构呈现{consumption_pattern}特征，{seasonal_analysis}，{consumption_stability_analysis}。",
        "去年消费质量{quality_analysis}，客单价控制{unit_price_analysis}，为今年发展{baseline_significance}。",
        "去年会员消费行为{behavior_analysis}，复购表现{repeat_purchase_analysis}。"
    ]

    # 平均消费数据分析模板句式（今年数据，包含对比）
    AVG_CONSUMPTION_THIS_YEAR_TEMPLATES = [
        "今年截至目前，首次消费金额月均{avg_first_consume:.1f}元，较去年同期{first_consume_comparison}，再次消费金额月均{avg_repeat_consume:.1f}元，{repeat_consume_comparison}。",
        "今年会员单均消费{avg_consume_amount:.1f}元，同比{consume_amount_trend}，会员人均贡献{avg_contribution:.1f}元，{contribution_comparison}。",
        "今年消费结构{structure_comparison}，{monthly_performance}，{consumption_quality_analysis}。",
        "对比去年同期，今年{year_over_year_analysis}，{consumption_optimization}。",
        "今年消费发展{development_assessment}，{future_projection}。"
    ]

    # 消费趋势分析词汇
    CONSUMPTION_TREND_TERMS = {
        "positive": ["呈现稳步上升趋势", "表现出良好增长态势", "显示积极发展势头", "展现强劲增长动力"],
        "negative": ["出现下降趋势", "表现相对疲软", "增长动力不足", "面临增长挑战"],
        "stable": ["保持相对稳定", "波动较小", "增长平稳", "发展稳健"],
        "volatile": ["波动较大", "起伏明显", "变化频繁", "不够稳定"]
    }

    # 消费质量评估词汇
    CONSUMPTION_QUALITY_TERMS = [
        "消费质量优秀，客单价控制良好",
        "消费质量稳健，有提升空间",
        "消费质量一般，需要改进",
        "消费质量偏低，需要重点关注"
    ]

    # 消费行为分析词汇
    CONSUMPTION_BEHAVIOR_TERMS = [
        "首次消费转化良好，复购意愿强烈",
        "首次消费门槛适中，复购需要激励",
        "首次消费偏低，复购表现一般",
        "首次消费和复购都需要优化"
    ]

    @staticmethod
    def get_avg_consumption_last_year_analysis_prompt(monthly_data: List[Dict[str, Any]]) -> str:
        """
        生成去年会员平均消费数据分析的按点格式提示词（洞见性+实操性）

        Args:
            monthly_data: 去年月度平均消费数据列表

        Returns:
            str: 按点格式的分析提示词
        """
        if not monthly_data:
            return "• 数据不足：去年会员平均消费数据缺失，建议完善数据收集机制"

        # 计算关键指标
        first_consume_amounts = [item.get('first_consume_amount', 0) for item in monthly_data]
        repeat_consume_amounts = [item.get('repeat_consume_amount', 0) for item in monthly_data]
        avg_consume_amounts = [item.get('avg_consume_amount', 0) for item in monthly_data]
        avg_contributions = [item.get('avg_contribution', 0) for item in monthly_data]

        # 基础统计
        avg_first_consume = sum(first_consume_amounts) / len(first_consume_amounts)
        avg_repeat_consume = sum(repeat_consume_amounts) / len(repeat_consume_amounts)
        avg_consume_amount = sum(avg_consume_amounts) / len(avg_consume_amounts)
        avg_contribution = sum(avg_contributions) / len(avg_contributions)

        # 找出峰值和低谷
        max_consume_idx = avg_consume_amounts.index(max(avg_consume_amounts))
        min_consume_idx = avg_consume_amounts.index(min(avg_consume_amounts))
        peak_month = monthly_data[max_consume_idx]['month']
        valley_month = monthly_data[min_consume_idx]['month']
        peak_value = max(avg_consume_amounts)
        valley_value = min(avg_consume_amounts)

        # 计算波动性
        variance = sum((x - avg_consume_amount) ** 2 for x in avg_consume_amounts) / len(avg_consume_amounts)
        volatility = (variance ** 0.5) / avg_consume_amount * 100 if avg_consume_amount > 0 else 0

        # 生成按点分析
        analysis_points = []

        # 1. 首次消费洞察 + 实操建议
        if avg_first_consume > 200:
            analysis_points.append(f"• 首次消费表现优秀：月均{avg_first_consume:.1f}元，门槛设置合理，建议保持现有获客策略并优化首次体验流程")
        elif avg_first_consume > 100:
            analysis_points.append(f"• 首次消费表现稳健：月均{avg_first_consume:.1f}元，有提升空间，建议优化首次消费引导并推出新客专享优惠")
        else:
            analysis_points.append(f"• 首次消费偏低：月均{avg_first_consume:.1f}元，需重点关注，建议降低首次消费门槛并加强新客转化策略")

        # 2. 复购消费洞察 + 实操建议
        repeat_ratio = (avg_repeat_consume / (avg_first_consume + avg_repeat_consume) * 100) if (avg_first_consume + avg_repeat_consume) > 0 else 0
        if repeat_ratio > 70:
            analysis_points.append(f"• 复购表现卓越：再次消费占比{repeat_ratio:.1f}%，月均{avg_repeat_consume:.1f}元，建议建立会员忠诚度体系并推出复购激励计划")
        elif repeat_ratio > 50:
            analysis_points.append(f"• 复购表现良好：再次消费占比{repeat_ratio:.1f}%，月均{avg_repeat_consume:.1f}元，建议加强会员运营并提升复购频次")
        else:
            analysis_points.append(f"• 复购需要改善：再次消费占比{repeat_ratio:.1f}%，月均{avg_repeat_consume:.1f}元，建议重新设计复购激励机制并优化产品体验")

        # 3. 单均消费洞察 + 实操建议
        if avg_consume_amount > 300:
            analysis_points.append(f"• 单均消费优异：月均{avg_consume_amount:.1f}元，客单价控制良好，建议推出高价值服务并建立VIP会员体系")
        elif avg_consume_amount > 150:
            analysis_points.append(f"• 单均消费稳健：月均{avg_consume_amount:.1f}元，有提升潜力，建议通过套餐组合和增值服务提升客单价")
        else:
            analysis_points.append(f"• 单均消费偏低：月均{avg_consume_amount:.1f}元，需要改进，建议优化产品定价策略并推出消费激励活动")

        # 4. 人均贡献洞察 + 实操建议
        contribution_efficiency = (avg_contribution / avg_consume_amount * 100) if avg_consume_amount > 0 else 0
        if contribution_efficiency > 80:
            analysis_points.append(f"• 贡献效率优秀：人均贡献{avg_contribution:.1f}元，转化效率{contribution_efficiency:.1f}%，建议扩大成功模式并加强精准营销")
        else:
            analysis_points.append(f"• 贡献效率待优化：人均贡献{avg_contribution:.1f}元，转化效率{contribution_efficiency:.1f}%，建议优化会员运营策略并提升价值转化")

        return "\n".join(analysis_points)

    @staticmethod
    def get_avg_consumption_this_year_analysis_prompt(
        this_year_data: List[Dict[str, Any]],
        last_year_data: List[Dict[str, Any]]
    ) -> str:
        """
        生成今年会员平均消费数据分析的按点格式提示词（对比分析+实操性）

        Args:
            this_year_data: 今年月度平均消费数据列表
            last_year_data: 去年同期数据列表

        Returns:
            str: 按点格式的对比分析提示词
        """
        if not this_year_data:
            return "• 数据不足：今年会员平均消费数据缺失，建议立即启动数据收集和分析机制"

        # 今年数据统计
        this_first_consume_amounts = [item.get('first_consume_amount', 0) for item in this_year_data]
        this_repeat_consume_amounts = [item.get('repeat_consume_amount', 0) for item in this_year_data]
        this_avg_consume_amounts = [item.get('avg_consume_amount', 0) for item in this_year_data]
        this_avg_contributions = [item.get('avg_contribution', 0) for item in this_year_data]

        this_avg_first_consume = sum(this_first_consume_amounts) / len(this_first_consume_amounts)
        this_avg_repeat_consume = sum(this_repeat_consume_amounts) / len(this_repeat_consume_amounts)
        this_avg_consume_amount = sum(this_avg_consume_amounts) / len(this_avg_consume_amounts)
        this_avg_contribution = sum(this_avg_contributions) / len(this_avg_contributions)

        # 对比分析（如果有去年数据）
        first_consume_growth = 0
        repeat_consume_growth = 0
        consume_amount_growth = 0
        contribution_growth = 0

        if last_year_data:
            # 取去年同期数据（相同月份数量）
            last_year_same_period = last_year_data[:len(this_year_data)]

            last_first_consume_amounts = [item.get('first_consume_amount', 0) for item in last_year_same_period]
            last_repeat_consume_amounts = [item.get('repeat_consume_amount', 0) for item in last_year_same_period]
            last_avg_consume_amounts = [item.get('avg_consume_amount', 0) for item in last_year_same_period]
            last_avg_contributions = [item.get('avg_contribution', 0) for item in last_year_same_period]

            last_avg_first_consume = sum(last_first_consume_amounts) / len(last_first_consume_amounts) if last_first_consume_amounts else 0
            last_avg_repeat_consume = sum(last_repeat_consume_amounts) / len(last_repeat_consume_amounts) if last_repeat_consume_amounts else 0
            last_avg_consume_amount = sum(last_avg_consume_amounts) / len(last_avg_consume_amounts) if last_avg_consume_amounts else 0
            last_avg_contribution = sum(last_avg_contributions) / len(last_avg_contributions) if last_avg_contributions else 0

            # 计算增长率
            first_consume_growth = ((this_avg_first_consume - last_avg_first_consume) / last_avg_first_consume * 100) if last_avg_first_consume > 0 else 0
            repeat_consume_growth = ((this_avg_repeat_consume - last_avg_repeat_consume) / last_avg_repeat_consume * 100) if last_avg_repeat_consume > 0 else 0
            consume_amount_growth = ((this_avg_consume_amount - last_avg_consume_amount) / last_avg_consume_amount * 100) if last_avg_consume_amount > 0 else 0
            contribution_growth = ((this_avg_contribution - last_avg_contribution) / last_avg_contribution * 100) if last_avg_contribution > 0 else 0

        # 生成按点分析
        analysis_points = []

        # 1. 首次消费对比洞察 + 实操建议
        if first_consume_growth > 15:
            analysis_points.append(f"• 首次消费增长卓越：较去年同期增长{first_consume_growth:.1f}%，月均{this_avg_first_consume:.1f}元，建议加大成功策略投入并扩展获客渠道")
        elif first_consume_growth > 0:
            analysis_points.append(f"• 首次消费稳步增长：较去年同期增长{first_consume_growth:.1f}%，月均{this_avg_first_consume:.1f}元，建议优化转化流程并提升新客体验")
        elif first_consume_growth > -10:
            analysis_points.append(f"• 首次消费略有下降：较去年同期下降{abs(first_consume_growth):.1f}%，月均{this_avg_first_consume:.1f}元，建议调整获客策略并优化首次消费引导")
        else:
            analysis_points.append(f"• 首次消费下降明显：较去年同期下降{abs(first_consume_growth):.1f}%，月均{this_avg_first_consume:.1f}元，建议重新评估获客策略并加强新客转化")

        # 2. 复购消费对比洞察 + 实操建议
        if repeat_consume_growth > 20:
            analysis_points.append(f"• 复购表现突出：较去年同期增长{repeat_consume_growth:.1f}%，月均{this_avg_repeat_consume:.1f}元，建议建立会员忠诚度计划并推出复购奖励机制")
        elif repeat_consume_growth > 0:
            analysis_points.append(f"• 复购持续改善：较去年同期增长{repeat_consume_growth:.1f}%，月均{this_avg_repeat_consume:.1f}元，建议加强会员运营并提升复购频次")
        else:
            analysis_points.append(f"• 复购需要关注：较去年同期变化{repeat_consume_growth:.1f}%，月均{this_avg_repeat_consume:.1f}元，建议重新设计复购激励策略并优化产品体验")

        # 3. 单均消费对比洞察 + 实操建议
        if consume_amount_growth > 10:
            analysis_points.append(f"• 单均消费提升显著：较去年同期增长{consume_amount_growth:.1f}%，月均{this_avg_consume_amount:.1f}元，建议推出高价值服务并建立差异化定价体系")
        elif consume_amount_growth > 0:
            analysis_points.append(f"• 单均消费稳步提升：较去年同期增长{consume_amount_growth:.1f}%，月均{this_avg_consume_amount:.1f}元，建议通过套餐组合和增值服务进一步提升客单价")
        else:
            analysis_points.append(f"• 单均消费需要改善：较去年同期变化{consume_amount_growth:.1f}%，月均{this_avg_consume_amount:.1f}元，建议优化产品定价策略并推出消费激励活动")

        # 4. 人均贡献对比洞察 + 实操建议
        if contribution_growth > 15:
            analysis_points.append(f"• 贡献效率大幅提升：较去年同期增长{contribution_growth:.1f}%，月均{this_avg_contribution:.1f}元，建议扩大成功模式并加强精准营销投入")
        elif contribution_growth > 0:
            analysis_points.append(f"• 贡献效率持续优化：较去年同期增长{contribution_growth:.1f}%，月均{this_avg_contribution:.1f}元，建议深化会员运营策略并提升价值转化率")
        else:
            analysis_points.append(f"• 贡献效率待提升：较去年同期变化{contribution_growth:.1f}%，月均{this_avg_contribution:.1f}元，建议重新评估会员价值策略并优化运营效率")

        return "\n".join(analysis_points)