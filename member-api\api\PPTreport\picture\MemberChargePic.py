# -*- coding: utf-8 -*-
"""
会员充值消费分析图片生成模块
生成会员充值实收金额、消耗储值金额、充值赠送金额和储值留存率的图表
"""

import datetime
import logging
import os
from typing import Dict, Any, List, Tuple, Optional
from pathlib import Path
import asyncio

# 设置matplotlib后端（在导入pyplot之前）
import matplotlib
matplotlib.use('Agg')  # 使用非交互式后端
import matplotlib.pyplot as plt
import matplotlib.dates as mdates
import pandas as pd
import numpy as np

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei', 'DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False

logger = logging.getLogger(__name__)

class MemberChargePicGenerator:
    """会员充值消费分析图片生成器"""

    def __init__(self, bid: str, image_manager):
        """
        初始化图片生成器

        Args:
            bid: 品牌ID
            image_manager: 图片管理器实例
        """
        self.bid = bid
        self.image_manager = image_manager

    def _extract_param(self, query_params, key, default=None):
        """
        从查询参数中提取值

        Args:
            query_params: 查询参数（对象或字典）
            key: 参数键
            default: 默认值

        Returns:
            参数值
        """
        if hasattr(query_params, key):
            return getattr(query_params, key)
        elif isinstance(query_params, dict):
            return query_params.get(key, default)
        else:
            return default

    async def generate_member_charge_charts(self, query_params) -> Dict[str, str]:
        """
        生成会员充值消费分析图表

        Args:
            query_params: 查询参数（对象或字典）

        Returns:
            Dict: 包含两张图片路径的字典
        """
        try:
            logger.info(f"开始生成会员充值消费分析图表 - bid: {self.bid}")
            logger.info(f"查询参数类型: {type(query_params)}")
            logger.info(f"图片管理器会话目录: {self.image_manager.session_dir}")

            # 计算时间范围
            current_date = datetime.datetime.now()
            end_date = self._extract_param(query_params, 'end_date', '2025-06-30')
            query_end_date = datetime.datetime.strptime(end_date, "%Y-%m-%d")

            logger.info(f"时间参数 - 当前日期: {current_date}, 查询结束日期: {query_end_date}")

            # 计算去年和今年的时间范围
            last_year = query_end_date.year - 1
            this_year = query_end_date.year

            # 去年：完整的12个月
            last_year_ranges = self._generate_monthly_ranges(last_year, 1, 12)

            # 今年：从1月到查询结束月份（如果是当前年份，则到上个月）
            if this_year == current_date.year:
                # 如果是当前年份，计算到上个月
                end_month = current_date.month - 1 if current_date.month > 1 else 1
            else:
                # 如果不是当前年份，使用查询结束日期的月份
                end_month = query_end_date.month

            this_year_ranges = self._generate_monthly_ranges(this_year, 1, end_month)

            # 获取数据
            logger.info(f"开始获取数据 - 去年范围: {len(last_year_ranges)}个月, 今年范围: {len(this_year_ranges)}个月")
            last_year_data = await self._fetch_monthly_data(last_year_ranges, query_params)
            this_year_data = await self._fetch_monthly_data(this_year_ranges, query_params)

            logger.info(f"数据获取结果 - 去年: {len(last_year_data)}条, 今年: {len(this_year_data)}条")

            # 初始化结果字典
            result = {}

            # 如果数据获取失败，生成错误图片
            if not last_year_data:
                logger.warning("去年数据获取失败，生成错误图片")
                error_path = self._generate_error_image("member_charge_last_year", "去年数据获取失败")
                if error_path:
                    result["member_charge_last_year"] = error_path

            if not this_year_data:
                logger.warning("今年数据获取失败，生成错误图片")
                error_path = self._generate_error_image("member_charge_this_year", "今年数据获取失败")
                if error_path:
                    result["member_charge_this_year"] = error_path

            logger.info(f"最终数据 - 去年: {len(last_year_data)}条, 今年: {len(this_year_data)}条")

            # 只有在有有效数据时才生成正常图片
            if last_year_data and self._has_valid_data(last_year_data):
                last_year_path = await self._generate_chart(
                    last_year_data,
                    f"{last_year}年会员充值消费分析",
                    "member_charge_last_year"
                )
                if last_year_path:
                    result["member_charge_last_year"] = last_year_path

            if this_year_data and self._has_valid_data(this_year_data):
                this_year_path = await self._generate_chart(
                    this_year_data,
                    f"{this_year}年会员充值消费分析",
                    "member_charge_this_year"
                )
                if this_year_path:
                    result["member_charge_this_year"] = this_year_path

            # 生成AI分析
            try:
                from .PictureAi import PictureAiAnalyzer
                ai_analyzer = PictureAiAnalyzer()

                logger.info("开始生成会员充值消费AI分析...")

                # 生成去年AI分析
                if last_year_data:
                    last_year_analysis = await ai_analyzer.generate_member_charge_analysis(
                        last_year_data, f"{last_year}年"
                    )
                    result["member_charge_last_year_analysis_report"] = last_year_analysis

                # 生成今年AI分析
                if this_year_data:
                    this_year_analysis = await ai_analyzer.generate_member_charge_analysis(
                        this_year_data, f"{this_year}年"
                    )
                    result["member_charge_this_year_analysis_report"] = this_year_analysis

                logger.info("会员充值消费AI分析生成完成")

            except Exception as ai_error:
                logger.error(f"生成AI分析失败: {ai_error}")
                # AI分析失败不影响图片生成

            logger.info(f"会员充值消费图表生成完成，共生成 {len(result)} 个结果")
            return result

        except Exception as e:
            logger.error(f"生成会员充值消费图表失败: {e}")
            return {}

    def _has_valid_data(self, data: List[Dict[str, Any]]) -> bool:
        """
        检查是否有有效的充值消费数据

        Args:
            data: 月度数据列表

        Returns:
            bool: 是否有有效数据
        """
        if not data:
            return False

        # 检查是否有任何月份有充值或消费数据
        for item in data:
            charge_amount = item.get('charge_amount', 0)
            period_charge_amount_unused = item.get('period_charge_amount_unused', 0)
            period_charge_present = item.get('period_charge_present', 0)
            retention_rate = item.get('retention_rate', 0)

            if charge_amount > 0 or period_charge_amount_unused > 0 or period_charge_present > 0 or retention_rate != 0:
                return True

        return False

    def _generate_error_image(self, image_type: str, error_message: str) -> str:
        """
        生成错误提示图片

        Args:
            image_type: 图片类型
            error_message: 错误信息

        Returns:
            str: 图片保存路径
        """
        try:
            # 创建错误图片
            fig, ax = plt.subplots(figsize=(12, 8))
            ax.text(0.5, 0.5, f'数据生成失败\n{error_message}',
                   horizontalalignment='center', verticalalignment='center',
                   fontsize=20, color='red', weight='bold',
                   transform=ax.transAxes)
            ax.set_xlim(0, 1)
            ax.set_ylim(0, 1)
            ax.axis('off')

            # 保存图片
            file_path = self.image_manager.get_image_path(image_type)
            plt.savefig(file_path, dpi=300, bbox_inches='tight', facecolor='white')
            plt.close(fig)

            logger.info(f"错误图片生成完成: {file_path}")
            return file_path

        except Exception as e:
            logger.error(f"生成错误图片失败: {e}")
            return ""

    def _generate_monthly_ranges(self, year: int, start_month: int, end_month: int) -> List[Tuple[str, str, str]]:
        """
        生成月度时间范围

        Args:
            year: 年份
            start_month: 开始月份
            end_month: 结束月份

        Returns:
            List: [(月份标签, 开始日期, 结束日期), ...]
        """
        ranges = []

        for month in range(start_month, end_month + 1):
            # 计算月份的第一天和最后一天
            first_day = datetime.date(year, month, 1)

            # 计算下个月的第一天，然后减去一天得到当月最后一天
            if month == 12:
                next_month_first = datetime.date(year + 1, 1, 1)
            else:
                next_month_first = datetime.date(year, month + 1, 1)

            last_day = next_month_first - datetime.timedelta(days=1)

            # 如果是当前月份且是今年，需要截止到昨天
            if year == datetime.datetime.now().year and month == datetime.datetime.now().month:
                yesterday = datetime.datetime.now().date() - datetime.timedelta(days=1)
                if yesterday < last_day:
                    last_day = yesterday

            month_label = f"{year}年{month}月"
            start_date = first_day.strftime("%Y-%m-%d")
            end_date = last_day.strftime("%Y-%m-%d")

            ranges.append((month_label, start_date, end_date))

        return ranges

    async def _fetch_monthly_data(self, time_ranges: List[Tuple[str, str, str]], query_params) -> List[Dict[str, Any]]:
        """
        获取月度充值消费数据

        Args:
            time_ranges: 时间范围列表
            query_params: 查询参数

        Returns:
            List: 月度充值消费数据列表
        """
        try:
            from api.query.MemberChargeSql import MemberChargeSqlQueries, MemberChargeCalculator
            from core.database import db

            monthly_data = []
            bid = self._extract_param(query_params, 'bid')
            sid = self._extract_param(query_params, 'sid', None)

            for month_label, start_date, end_date in time_ranges:
                logger.info(f"获取 {month_label} 充值消费数据: {start_date} 到 {end_date}")

                try:
                    # 转换日期格式为YYYYMMDD
                    start_date_db = start_date.replace('-', '')
                    end_date_db = end_date.replace('-', '')

                    # 1. 获取充值基础数据
                    base_sql = MemberChargeSqlQueries.build_dwoutput_charge_base_sql(
                        start_date_db, end_date_db, bid, sid
                    )
                    base_result = await db.execute_dwoutput_one(base_sql)
                    base_result = base_result if base_result else {}

                    # 2. 获取储值消耗数据
                    consume_sql = f"""
                    SELECT {MemberChargeSqlQueries.get_dwoutput_total_consume_prepay_used_sql(start_date_db, end_date_db, bid, sid)}
                    """
                    consume_result = await db.execute_dwoutput_one(consume_sql)
                    consume_result = consume_result if consume_result else {}

                    # 3. 处理金额字段（数据库存储的是真实值*100）
                    money_fields = ['total_charge_cash', 'total_charge_amount', 'total_charge_present', 'total_consume_prepay_used']
                    base_result = MemberChargeCalculator.process_money_fields(base_result, money_fields)
                    consume_result = MemberChargeCalculator.process_money_fields(consume_result, money_fields)

                    # 4. 合并数据
                    merged_data = MemberChargeCalculator.merge_charge_data(base_result, consume_result)

                    # 5. 提取所需字段
                    charge_amount = merged_data.get('total_charge_cash', 0) or 0  # 期间充值实收总金额
                    period_charge_amount_unused = merged_data.get('total_consume_prepay_used', 0) or 0  # 期间消耗储值实收总金额
                    period_charge_present = merged_data.get('total_charge_present', 0) or 0  # 期间充值赠送金额
                    retention_rate = merged_data.get('prepay_retention_rate', 0) or 0  # 储值留存率

                    logger.info(f"{month_label} 数据: 充值实收{charge_amount:.2f}, 消耗储值{period_charge_amount_unused:.2f}, 充值赠送{period_charge_present:.2f}, 留存率{retention_rate:.2f}%")

                    monthly_data.append({
                        'month': month_label,
                        'charge_amount': float(charge_amount),
                        'period_charge_amount_unused': float(period_charge_amount_unused),
                        'period_charge_present': float(period_charge_present),
                        'retention_rate': float(retention_rate)
                    })

                except Exception as month_error:
                    logger.error(f"获取 {month_label} 数据失败: {month_error}")
                    # 添加失败的月份数据，避免图表中断
                    monthly_data.append({
                        'month': month_label,
                        'charge_amount': 0.0,
                        'period_charge_amount_unused': 0.0,
                        'period_charge_present': 0.0,
                        'retention_rate': 0.0
                    })

            logger.info(f"充值消费数据获取完成，共 {len(monthly_data)} 条记录")
            return monthly_data

        except Exception as e:
            logger.error(f"获取月度充值消费数据失败: {e}")
            return []

    async def _generate_chart(self, data: List[Dict[str, Any]], title: str, image_type: str) -> str:
        """
        生成充值消费分析图表

        Args:
            data: 月度数据列表
            title: 图表标题
            image_type: 图片类型

        Returns:
            str: 图片保存路径
        """
        try:
            if not data:
                logger.warning(f"数据为空，无法生成图表: {image_type}")
                return ""

            # 创建图表
            fig, ax1 = plt.subplots(figsize=(14, 8))

            # 提取数据
            months = [item['month'] for item in data]
            charge_amounts = [item['charge_amount'] for item in data]
            period_charge_amount_unused = [item['period_charge_amount_unused'] for item in data]
            period_charge_presents = [item['period_charge_present'] for item in data]
            retention_rates = [item['retention_rate'] for item in data]

            # 设置X轴位置
            x_pos = range(len(months))

            # 绘制柱状图
            bar_width = 0.25
            bars1 = ax1.bar([x - bar_width for x in x_pos], charge_amounts,
                           bar_width, label='期间充值实收总金额', color='#E74C3C', alpha=0.8)
            bars2 = ax1.bar(x_pos, period_charge_amount_unused,
                           bar_width, label='期间消耗储值实收总金额', color='#4472C4', alpha=0.8)
            bars3 = ax1.bar([x + bar_width for x in x_pos], period_charge_presents,
                           bar_width, label='期间充值赠送金额', color='#FF8C00', alpha=0.8)

            # 设置左侧Y轴（金额）
            ax1.set_xlabel('月份', fontsize=12, fontweight='bold')
            ax1.set_ylabel('金额（元）', fontsize=12, fontweight='bold', color='black')
            ax1.tick_params(axis='y', labelcolor='black')

            # 设置X轴标签
            ax1.set_xticks(x_pos)
            ax1.set_xticklabels(months, rotation=45, ha='right')

            # 添加数值标签到柱状图
            for bars in [bars1, bars2, bars3]:
                for bar in bars:
                    height = bar.get_height()
                    if height > 0:
                        ax1.annotate(f'{height:.0f}',
                                   xy=(bar.get_x() + bar.get_width() / 2, height),
                                   xytext=(0, 3),  # 3 points vertical offset
                                   textcoords="offset points",
                                   ha='center', va='bottom', fontsize=9)

            # 创建右侧Y轴用于储值留存率
            ax2 = ax1.twinx()
            line = ax2.plot(x_pos, retention_rates, color='#2ECC71', marker='o', linewidth=2,
                           markersize=6, label='储值留存率')
            ax2.set_ylabel('储值留存率（%）', fontsize=12, fontweight='bold', color='#2ECC71')
            ax2.tick_params(axis='y', labelcolor='#2ECC71')

            # 添加数值标签到折线图
            for i, rate in enumerate(retention_rates):
                ax2.annotate(f'{rate:.1f}%',
                           xy=(i, rate),
                           xytext=(0, 10),  # 10 points vertical offset
                           textcoords="offset points",
                           ha='center', va='bottom', fontsize=9, color='#2ECC71')

            # 设置标题
            plt.title(title, fontsize=16, fontweight='bold', pad=20)

            # 合并图例
            lines1, labels1 = ax1.get_legend_handles_labels()
            lines2, labels2 = ax2.get_legend_handles_labels()
            ax1.legend(lines1 + lines2, labels1 + labels2, loc='upper left', fontsize=11)

            # 添加数据表格
            self._add_data_table(data, ax1)

            # 调整布局
            plt.tight_layout()

            # 保存图片
            save_path = self.image_manager.get_image_path(image_type)
            logger.info(f"准备保存图片到: {save_path}")

            # 确保保存目录存在
            save_dir = Path(save_path).parent
            save_dir.mkdir(parents=True, exist_ok=True)
            logger.info(f"保存目录: {save_dir}, 存在: {save_dir.exists()}")

            # 保存图片
            try:
                plt.savefig(save_path, dpi=300, bbox_inches='tight',
                           facecolor='white', edgecolor='none')
                logger.info(f"matplotlib保存完成")

                # 验证文件是否真的被创建
                if Path(save_path).exists():
                    file_size = Path(save_path).stat().st_size
                    logger.info(f"图表生成成功: {save_path}, 文件大小: {file_size} 字节")
                else:
                    logger.error(f"图片保存失败: 文件未创建 {save_path}")
                    return ""

            except Exception as save_error:
                logger.error(f"matplotlib保存失败: {save_error}")
                return ""
            finally:
                plt.close()

            return save_path

        except Exception as e:
            logger.error(f"生成图表失败 {image_type}: {e}")
            return ""

    def _add_data_table(self, data: List[Dict[str, Any]], ax):
        """
        在图表下方添加数据表格（横向布局，时间为横轴）

        Args:
            data: 月度数据列表
            ax: matplotlib轴对象
        """
        try:
            if not data:
                return

            # 准备表格数据
            months = [item['month'] for item in data]
            charge_amounts = [f"{item['charge_amount']:.0f}" for item in data]
            period_charge_amount_unused = [f"{item['period_charge_amount_unused']:.0f}" for item in data]
            period_charge_presents = [f"{item['period_charge_present']:.0f}" for item in data]
            retention_rates = [f"{item['retention_rate']:.1f}%" for item in data]

            # 构建表格数据（行为指标，列为月份）
            table_data = [
                charge_amounts,  # 期间充值实收总金额
                period_charge_amount_unused,  # 期间消耗储值实收总金额
                period_charge_presents,  # 期间充值赠送金额
                retention_rates  # 储值留存率
            ]

            # 行标签
            row_labels = [
                '期间充值实收总金额(元)',
                '期间消耗储值实收总金额(元)',
                '期间充值赠送金额(元)',
                '储值留存率(%)'
            ]

            # 创建表格
            table = ax.table(
                cellText=table_data,
                rowLabels=row_labels,
                colLabels=months,
                cellLoc='center',
                loc='bottom',
                bbox=[0, -0.6, 1, 0.4]  # [x, y, width, height]
            )

            # 设置表格样式
            table.auto_set_font_size(False)
            table.set_fontsize(9)
            table.scale(1, 1.5)

            # 设置表头样式
            for i in range(len(months)):
                table[(0, i)].set_facecolor('#E6E6FA')
                table[(0, i)].set_text_props(weight='bold')

            # 设置行标签样式
            for i in range(len(row_labels)):
                table[(i + 1, -1)].set_facecolor('#F0F8FF')
                table[(i + 1, -1)].set_text_props(weight='bold')

            # 设置数据单元格样式
            for i in range(len(row_labels)):
                for j in range(len(months)):
                    if i == 3:  # 储值留存率行使用不同颜色
                        table[(i + 1, j)].set_facecolor('#F0FFF0')
                    else:
                        table[(i + 1, j)].set_facecolor('#FFFFFF')

            logger.info(f"数据表格添加完成，{len(row_labels)}行 x {len(months)}列")

        except Exception as e:
            logger.error(f"添加数据表格失败: {e}")


def create_member_charge_pic_generator(bid: str, image_manager):
    """
    创建会员充值消费图片生成器

    Args:
        bid: 品牌ID
        image_manager: 图片管理器实例

    Returns:
        MemberChargePicGenerator: 图片生成器实例
    """
    return MemberChargePicGenerator(bid, image_manager)